import React, { useState, useEffect } from 'react';
import { Clock, Zap, Gift, Star, Users, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface FlashSalesBannerProps {
  translations: {
    flashSale: {
      title: string;
      subtitle: string;
      discount: string;
      timeLeft: string;
      days: string;
      hours: string;
      minutes: string;
      seconds: string;
      bookNow: string;
      slotsLeft: string;
      todayOnly: string;
      limitedSpots: string;
      lastChance: string;
      popularTreatment: string;
      realTimeBookings: string;
      justBooked: string;
    };
  };
  language?: string;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const FlashSalesBanner: React.FC<FlashSalesBannerProps> = ({ 
  translations, 
  language = 'en' 
}) => {
  // Flash sale end time (24 hours from now for demo)
  const [targetDate] = useState(() => {
    const now = new Date();
    return new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours from now
  });

  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [isVisible, setIsVisible] = useState(true);
  const [spotsLeft, setSpotsLeft] = useState(8);
  const [recentBookings, setRecentBookings] = useState<string[]>([]);
  const [showBookingAlert, setShowBookingAlert] = useState(false);

  // Countdown timer
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date().getTime();
      const distance = targetDate.getTime() - now;

      if (distance > 0) {
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setIsVisible(false);
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [targetDate]);

  // Simulate real-time bookings
  useEffect(() => {
    const names = [
      'Sarah M.', 'Mike L.', 'Anna K.', 'David P.', 'Lisa W.',
      'Tom R.', 'Emma S.', 'John D.', 'Sofia C.', 'Alex B.'
    ];

    const locations = [
      'Bangkok', 'Sukhumvit', 'Silom', 'Sathorn', 'Thonglor',
      'Phrom Phong', 'Asok', 'Siam', 'Ratchada', 'Huai Khwang'
    ];

    const bookingInterval = setInterval(() => {
      if (Math.random() > 0.3) { // 70% chance of booking notification
        const randomName = names[Math.floor(Math.random() * names.length)];
        const randomLocation = locations[Math.floor(Math.random() * locations.length)];
        const booking = `${randomName} from ${randomLocation}`;
        
        setRecentBookings(prev => [booking, ...prev.slice(0, 2)]); // Keep only 3 recent
        setShowBookingAlert(true);
        
        // Decrease spots randomly
        if (Math.random() > 0.5 && spotsLeft > 1) {
          setSpotsLeft(prev => prev - 1);
        }
        
        setTimeout(() => setShowBookingAlert(false), 4000);
      }
    }, 8000); // Every 8 seconds

    return () => clearInterval(bookingInterval);
  }, [spotsLeft]);

  if (!isVisible) return null;

  const isUrgent = timeLeft.hours < 6;
  const isCritical = timeLeft.hours < 2;

  return (
    <div className={cn(
      "relative overflow-hidden transition-all duration-500",
      isCritical ? "bg-gradient-to-r from-red-500 via-pink-500 to-red-600" :
      isUrgent ? "bg-gradient-to-r from-orange-500 via-red-500 to-pink-600" :
      "bg-gradient-to-r from-primary-500 via-purple-500 to-pink-600"
    )}>
      {/* Animated background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 animate-pulse"></div>
      </div>

      <div className="relative container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
          
          {/* Left Section - Main Offer */}
          <div className="flex-1 text-center lg:text-left">
            <div className="flex items-center justify-center lg:justify-start gap-2 mb-2">
              <Zap className="h-5 w-5 text-yellow-300 animate-pulse" />
              <Badge variant="secondary" className="bg-yellow-400 text-black font-bold px-3 py-1">
                {translations.flashSale.todayOnly}
              </Badge>
              <Star className="h-4 w-4 text-yellow-300" />
            </div>
            
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-2">
              {translations.flashSale.title}
            </h2>
            
            <p className="text-lg md:text-xl text-white/90 mb-4">
              {translations.flashSale.subtitle}
            </p>

            <div className="flex items-center justify-center lg:justify-start gap-4 text-white/90">
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {translations.flashSale.slotsLeft}: 
                  <span className={cn(
                    "ml-1 font-bold",
                    spotsLeft <= 3 ? "text-yellow-300 animate-pulse" : "text-white"
                  )}>
                    {spotsLeft}
                  </span>
                </span>
              </div>
              
              {spotsLeft <= 5 && (
                <div className="flex items-center gap-1 animate-bounce">
                  <AlertCircle className="h-4 w-4 text-yellow-300" />
                  <span className="text-sm font-medium text-yellow-300">
                    {translations.flashSale.limitedSpots}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Center Section - Countdown Timer */}
          <div className="flex-shrink-0">
            <div className="text-center">
              <p className="text-white/90 text-sm font-medium mb-2 flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {translations.flashSale.timeLeft}
              </p>
              
              <div className="grid grid-cols-4 gap-2 md:gap-4">
                {/* Days */}
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 min-w-[60px]">
                  <div className="text-2xl md:text-3xl font-bold text-white">
                    {timeLeft.days.toString().padStart(2, '0')}
                  </div>
                  <div className="text-xs text-white/80 uppercase tracking-wide">
                    {translations.flashSale.days}
                  </div>
                </div>
                
                {/* Hours */}
                <div className={cn(
                  "bg-white/20 backdrop-blur-sm rounded-lg p-3 min-w-[60px]",
                  isUrgent && "animate-pulse bg-red-500/30"
                )}>
                  <div className="text-2xl md:text-3xl font-bold text-white">
                    {timeLeft.hours.toString().padStart(2, '0')}
                  </div>
                  <div className="text-xs text-white/80 uppercase tracking-wide">
                    {translations.flashSale.hours}
                  </div>
                </div>
                
                {/* Minutes */}
                <div className={cn(
                  "bg-white/20 backdrop-blur-sm rounded-lg p-3 min-w-[60px]",
                  isCritical && "animate-pulse bg-red-500/40"
                )}>
                  <div className="text-2xl md:text-3xl font-bold text-white">
                    {timeLeft.minutes.toString().padStart(2, '0')}
                  </div>
                  <div className="text-xs text-white/80 uppercase tracking-wide">
                    {translations.flashSale.minutes}
                  </div>
                </div>
                
                {/* Seconds */}
                <div className="bg-white/20 backdrop-blur-sm rounded-lg p-3 min-w-[60px] animate-pulse">
                  <div className="text-2xl md:text-3xl font-bold text-white">
                    {timeLeft.seconds.toString().padStart(2, '0')}
                  </div>
                  <div className="text-xs text-white/80 uppercase tracking-wide">
                    {translations.flashSale.seconds}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Section - CTA */}
          <div className="flex-shrink-0 text-center">
            <div className="mb-4">
              <div className="text-4xl md:text-5xl font-black text-yellow-300 mb-1">
                {translations.flashSale.discount}
              </div>
              <div className="text-white/90 text-sm uppercase tracking-wide">
                {translations.flashSale.popularTreatment}
              </div>
            </div>
            
            <Button 
              size="lg"
              className={cn(
                "bg-white text-black hover:bg-yellow-100 font-bold px-8 py-3 text-lg shadow-xl transform hover:scale-105 transition-all duration-200",
                isCritical && "animate-bounce"
              )}
            >
              <Gift className="h-5 w-5 mr-2" />
              {translations.flashSale.bookNow}
            </Button>
            
            {isCritical && (
              <p className="text-yellow-300 text-sm font-bold mt-2 animate-pulse">
                {translations.flashSale.lastChance}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Real-time booking notifications */}
      {showBookingAlert && recentBookings.length > 0 && (
        <div className="absolute top-4 right-4 lg:top-auto lg:bottom-4 lg:right-4">
          <div className="bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg animate-slide-in-right">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">
                {translations.flashSale.justBooked}: {recentBookings[0]}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Pulse effect for urgency */}
      {isCritical && (
        <div className="absolute inset-0 border-4 border-yellow-400 animate-ping opacity-20 rounded-lg"></div>
      )}
    </div>
  );
};

export default FlashSalesBanner;