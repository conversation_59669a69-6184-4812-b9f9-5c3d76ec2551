
import React, { useState } from 'react';
import { Menu, X, Globe, Phone, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface NavigationProps {
  currentLang: string;
  onLanguageChange: (lang: string) => void;
  translations: {
    openHours: string;
    nav: {
      home: string;
      services: string;
      about: string;
      gallery: string;
      blog: string;
      bookNow: string;
    };
  };
}

const Navigation = ({ currentLang, onLanguageChange, translations }: NavigationProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const languages = [
    { code: 'th', name: 'ไทย', flag: '🇹🇭' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'zh', name: '中文', flag: '🇨🇳' }
  ];

  const currentLanguage = languages.find(lang => lang.code === currentLang);

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-border/50 sticky top-0 z-50">
      {/* Top bar */}
      <div className="bg-medical-pink border-b border-primary-200">
        <div className="container mx-auto px-4 py-2">
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-primary-600" />
                <span className="text-primary-700">************</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-primary-600" />
                <span className="text-primary-700">{translations.openHours}</span>
              </div>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-primary-700 hover:bg-primary-100">
                  <Globe className="h-4 w-4 mr-2" />
                  {currentLanguage?.flag} {currentLanguage?.name}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {languages.map((lang) => (
                  <DropdownMenuItem
                    key={lang.code}
                    onClick={() => onLanguageChange(lang.code)}
                    className={currentLang === lang.code ? 'bg-primary-50' : ''}
                  >
                    <span className="mr-2">{lang.flag}</span>
                    {lang.name}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Main navigation */}
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-2">
            <img 
              src="/logo.svg" 
              alt="Lullaby Clinic Logo" 
              className="w-8 h-8"
            />
            <span className="text-xl font-bold text-primary-700">Lullaby Clinic</span>
          </div>

          {/* Desktop menu */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="#home" className="text-foreground hover:text-primary-600 transition-colors">
              {translations.nav.home}
            </a>
            <a href="#services" className="text-foreground hover:text-primary-600 transition-colors">
              {translations.nav.services}
            </a>
            <a href="#about" className="text-foreground hover:text-primary-600 transition-colors">
              {translations.nav.about}
            </a>
            <a href="#gallery" className="text-foreground hover:text-primary-600 transition-colors">
              {translations.nav.gallery}
            </a>
            <a href="#blog" className="text-foreground hover:text-primary-600 transition-colors">
              {translations.nav.blog}
            </a>
            <Button className="bg-primary-500 hover:bg-primary-600 text-white">
              {translations.nav.bookNow}
            </Button>
          </div>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-border/50 py-4 animate-slide-in">
            <div className="flex flex-col space-y-4">
              <a href="#home" className="text-foreground hover:text-primary-600 transition-colors py-2">
                {translations.nav.home}
              </a>
              <a href="#services" className="text-foreground hover:text-primary-600 transition-colors py-2">
                {translations.nav.services}
              </a>
              <a href="#about" className="text-foreground hover:text-primary-600 transition-colors py-2">
                {translations.nav.about}
              </a>
              <a href="#gallery" className="text-foreground hover:text-primary-600 transition-colors py-2">
                {translations.nav.gallery}
              </a>
              <a href="#blog" className="text-foreground hover:text-primary-600 transition-colors py-2">
                {translations.nav.blog}
              </a>
              <Button className="bg-primary-500 hover:bg-primary-600 text-white w-full mt-4">
                {translations.nav.bookNow}
              </Button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
