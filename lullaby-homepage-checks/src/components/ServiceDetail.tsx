import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>, Star, CheckCircle, AlertTriangle, Calendar, Award, Shield, Heart, Zap, ArrowLeft, Phone, MapPin } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON>Content, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import LazyImage from '@/components/LazyImage';
import { cn } from '@/lib/utils';

interface ServiceDetailProps {
  service: {
    id: string;
    name: string;
    category: string;
    shortDescription: string;
    fullDescription: string;
    duration: string;
    price: {
      original: number;
      discounted?: number;
      currency: string;
    };
    popularityScore: number;
    availableSlots: number;
    totalSlots: number;
    benefits: string[];
    procedure: string[];
    aftercare: string[];
    beforeAfter: {
      before: string;
      after: string;
      description: string;
    }[];
    testimonials: {
      name: string;
      rating: number;
      comment: string;
      date: string;
      verified: boolean;
    }[];
    faq: {
      question: string;
      answer: string;
    }[];
  };
  translations: {
    serviceDetail: {
      backToServices: string;
      bookNow: string;
      callNow: string;
      whatsApp: string;
      overview: string;
      beforeAfter: string;
      testimonials: string;
      faq: string;
      procedure: string;
      aftercare: string;
      benefits: string;
      duration: string;
      price: string;
      originalPrice: string;
      discountedPrice: string;
      popularityScore: string;
      availableSlots: string;
      limitedAvailability: string;
      almostFullyBooked: string;
      highDemand: string;
      verified: string;
      recommended: string;
      todaySpecial: string;
      bookingUrgency: string;
      peopleViewing: string;
      lastBooking: string;
      satisfaction: string;
      safetyFirst: string;
      expertCare: string;
      premiumQuality: string;
    };
  };
  onBack?: () => void;
}

const ServiceDetail: React.FC<ServiceDetailProps> = ({ service, translations, onBack }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [currentViewers, setCurrentViewers] = useState(0);
  const [lastBookingTime, setLastBookingTime] = useState('');
  const [showBookingAlert, setShowBookingAlert] = useState(false);

  // Simulate real-time activity
  useEffect(() => {
    // Simulate current viewers
    const viewersInterval = setInterval(() => {
      setCurrentViewers(Math.floor(Math.random() * 12) + 3); // 3-15 viewers
    }, 5000);

    // Simulate last booking time
    const bookingTimes = ['2 minutes ago', '5 minutes ago', '8 minutes ago', '12 minutes ago'];
    setLastBookingTime(bookingTimes[Math.floor(Math.random() * bookingTimes.length)]);

    // Show booking alert periodically
    const alertInterval = setInterval(() => {
      if (Math.random() > 0.6) {
        setShowBookingAlert(true);
        setTimeout(() => setShowBookingAlert(false), 4000);
      }
    }, 15000);

    return () => {
      clearInterval(viewersInterval);
      clearInterval(alertInterval);
    };
  }, []);

  const availabilityPercentage = (service.availableSlots / service.totalSlots) * 100;
  const isHighDemand = availabilityPercentage < 30;
  const isAlmostFull = availabilityPercentage < 15;

  const getUrgencyLevel = () => {
    if (isAlmostFull) return { level: 'critical', color: 'text-red-600', bgColor: 'bg-red-50 border-red-200' };
    if (isHighDemand) return { level: 'high', color: 'text-orange-600', bgColor: 'bg-orange-50 border-orange-200' };
    return { level: 'normal', color: 'text-green-600', bgColor: 'bg-green-50 border-green-200' };
  };

  const urgency = getUrgencyLevel();

  return (
    <div className="min-h-screen bg-gradient-to-br from-medical-softPink to-white">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              onClick={onBack}
              className="flex items-center gap-2 text-muted-foreground hover:text-primary-600"
            >
              <ArrowLeft className="h-4 w-4" />
              {translations.serviceDetail.backToServices}
            </Button>
            
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                {translations.serviceDetail.peopleViewing}: 
                <span className="font-semibold text-primary-600 ml-1">{currentViewers}</span>
              </div>
              
              {lastBookingTime && (
                <div className="flex items-center gap-1 text-sm text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  {translations.serviceDetail.lastBooking}: {lastBookingTime}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            
            {/* Service Header */}
            <div className="bg-white rounded-xl shadow-soft p-8">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-4">
                    <Badge variant="secondary" className="bg-primary-100 text-primary-700">
                      {service.category}
                    </Badge>
                    
                    {service.popularityScore >= 90 && (
                      <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                        <Star className="h-3 w-3 mr-1" />
                        {translations.serviceDetail.recommended}
                      </Badge>
                    )}
                    
                    {service.price.discounted && (
                      <Badge className="bg-red-100 text-red-800 border-red-200">
                        <Zap className="h-3 w-3 mr-1" />
                        {translations.serviceDetail.todaySpecial}
                      </Badge>
                    )}
                  </div>
                  
                  <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                    {service.name}
                  </h1>
                  
                  <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                    {service.shortDescription}
                  </p>
                  
                  {/* Key Stats */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg">
                      <Clock className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                      <div className="text-sm text-muted-foreground">{translations.serviceDetail.duration}</div>
                      <div className="font-semibold text-foreground">{service.duration}</div>
                    </div>
                    
                    <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg">
                      <Users className="h-6 w-6 text-green-600 mx-auto mb-2" />
                      <div className="text-sm text-muted-foreground">{translations.serviceDetail.satisfaction}</div>
                      <div className="font-semibold text-foreground">{service.popularityScore}%</div>
                    </div>
                    
                    <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg">
                      <Award className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                      <div className="text-sm text-muted-foreground">{translations.serviceDetail.verified}</div>
                      <div className="font-semibold text-foreground">✓ {translations.serviceDetail.expertCare}</div>
                    </div>
                  </div>
                </div>
                
                {/* Featured Image */}
                <div className="w-full md:w-80 h-64 md:h-80">
                  <LazyImage
                    src="/placeholder.svg"
                    alt={service.name}
                    className="w-full h-full rounded-xl"
                    objectFit="cover"
                    priority={true}
                  />
                </div>
              </div>
            </div>

            {/* Tabs Content */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-5 bg-white shadow-sm">
                <TabsTrigger value="overview">{translations.serviceDetail.overview}</TabsTrigger>
                <TabsTrigger value="procedure">{translations.serviceDetail.procedure}</TabsTrigger>
                <TabsTrigger value="beforeafter">{translations.serviceDetail.beforeAfter}</TabsTrigger>
                <TabsTrigger value="testimonials">{translations.serviceDetail.testimonials}</TabsTrigger>
                <TabsTrigger value="faq">{translations.serviceDetail.faq}</TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Heart className="h-5 w-5 text-primary-600" />
                      {translations.serviceDetail.benefits}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {service.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
                          <span className="text-muted-foreground">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>{translations.serviceDetail.overview}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none text-muted-foreground">
                      <p className="leading-relaxed">{service.fullDescription}</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Procedure Tab */}
              <TabsContent value="procedure" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5 text-primary-600" />
                      {translations.serviceDetail.procedure}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {service.procedure.map((step, index) => (
                        <div key={index} className="flex gap-4">
                          <div className="flex-shrink-0 w-8 h-8 bg-primary-100 text-primary-700 rounded-full flex items-center justify-center font-semibold text-sm">
                            {index + 1}
                          </div>
                          <div className="flex-1 pt-1">
                            <p className="text-muted-foreground leading-relaxed">{step}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Heart className="h-5 w-5 text-pink-600" />
                      {translations.serviceDetail.aftercare}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {service.aftercare.map((care, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-pink-600 flex-shrink-0 mt-0.5" />
                          <span className="text-muted-foreground">{care}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Before/After Tab */}
              <TabsContent value="beforeafter" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {service.beforeAfter.map((item, index) => (
                    <Card key={index} className="overflow-hidden">
                      <div className="grid grid-cols-2 h-64">
                        <div className="relative">
                          <LazyImage
                            src={item.before}
                            alt={`Before ${service.name}`}
                            className="w-full h-full"
                            objectFit="cover"
                          />
                          <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                            Before
                          </div>
                        </div>
                        <div className="relative">
                          <LazyImage
                            src={item.after}
                            alt={`After ${service.name}`}
                            className="w-full h-full"
                            objectFit="cover"
                          />
                          <div className="absolute bottom-2 right-2 bg-primary-600 text-white px-2 py-1 rounded text-xs">
                            After
                          </div>
                        </div>
                      </div>
                      <CardContent className="p-4">
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              {/* Testimonials Tab */}
              <TabsContent value="testimonials" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {service.testimonials.map((testimonial, index) => (
                    <Card key={index}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={cn(
                                  "h-4 w-4",
                                  i < testimonial.rating ? "text-yellow-500 fill-current" : "text-gray-300"
                                )}
                              />
                            ))}
                          </div>
                          {testimonial.verified && (
                            <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              {translations.serviceDetail.verified}
                            </Badge>
                          )}
                        </div>
                        <blockquote className="text-muted-foreground mb-4 italic">
                          "{testimonial.comment}"
                        </blockquote>
                        <div className="flex justify-between items-center text-sm">
                          <span className="font-medium text-foreground">{testimonial.name}</span>
                          <span className="text-muted-foreground">{testimonial.date}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              {/* FAQ Tab */}
              <TabsContent value="faq" className="space-y-4">
                {service.faq.map((item, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="text-lg text-foreground">{item.question}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground leading-relaxed">{item.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar - Booking Widget */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-6">
              
              {/* Pricing & Booking Card */}
              <Card className={cn("shadow-xl", urgency.bgColor)}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl">
                      {service.price.discounted ? translations.serviceDetail.todaySpecial : translations.serviceDetail.price}
                    </CardTitle>
                    {isHighDemand && (
                      <Badge className={urgency.color.replace('text-', 'bg-').replace('-600', '-100') + ' ' + urgency.color}>
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {translations.serviceDetail.highDemand}
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  
                  {/* Pricing */}
                  <div className="text-center">
                    {service.price.discounted ? (
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground line-through">
                          {translations.serviceDetail.originalPrice}: {service.price.original} {service.price.currency}
                        </div>
                        <div className="text-3xl font-bold text-primary-600">
                          {service.price.discounted} {service.price.currency}
                        </div>
                        <div className="text-sm text-green-600 font-medium">
                          Save {service.price.original - service.price.discounted} {service.price.currency}
                        </div>
                      </div>
                    ) : (
                      <div className="text-3xl font-bold text-foreground">
                        {service.price.original} {service.price.currency}
                      </div>
                    )}
                  </div>

                  {/* Availability */}
                  <div className="space-y-3">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-muted-foreground">{translations.serviceDetail.availableSlots}</span>
                      <span className={urgency.color + " font-medium"}>
                        {service.availableSlots}/{service.totalSlots}
                      </span>
                    </div>
                    
                    <Progress 
                      value={100 - availabilityPercentage} 
                      className="h-2"
                    />
                    
                    <div className={cn("text-sm text-center font-medium", urgency.color)}>
                      {isAlmostFull ? translations.serviceDetail.almostFullyBooked :
                       isHighDemand ? translations.serviceDetail.limitedAvailability :
                       translations.serviceDetail.availableSlots}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-3">
                    <Button 
                      className={cn(
                        "w-full py-4 text-lg font-semibold",
                        isAlmostFull && "animate-pulse"
                      )}
                      size="lg"
                    >
                      <Calendar className="h-5 w-5 mr-2" />
                      {translations.serviceDetail.bookNow}
                    </Button>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <Button variant="outline" className="flex items-center justify-center gap-2">
                        <Phone className="h-4 w-4" />
                        {translations.serviceDetail.callNow}
                      </Button>
                      
                      <Button variant="outline" className="flex items-center justify-center gap-2 text-green-600 border-green-600 hover:bg-green-50">
                        <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.687"/>
                        </svg>
                        WhatsApp
                      </Button>
                    </div>
                  </div>

                  {/* Trust Indicators */}
                  <div className="pt-4 border-t border-border/50">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div className="space-y-1">
                        <Shield className="h-5 w-5 text-blue-600 mx-auto" />
                        <div className="text-xs text-muted-foreground">{translations.serviceDetail.safetyFirst}</div>
                      </div>
                      <div className="space-y-1">
                        <Award className="h-5 w-5 text-yellow-600 mx-auto" />
                        <div className="text-xs text-muted-foreground">{translations.serviceDetail.expertCare}</div>
                      </div>
                      <div className="space-y-1">
                        <Star className="h-5 w-5 text-purple-600 mx-auto" />
                        <div className="text-xs text-muted-foreground">{translations.serviceDetail.premiumQuality}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Info */}
              <Card>
                <CardContent className="p-4">
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-primary-600" />
                      <span className="text-primary-600 font-medium">************</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Bangkok, Thailand</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Mon-Sat: 9AM-6PM</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Booking Alert */}
      {showBookingAlert && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-xl animate-slide-in-right z-50">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            <span className="font-medium">Someone just booked this service!</span>
          </div>
          <div className="text-sm opacity-90 mt-1">
            {translations.serviceDetail.bookingUrgency}
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceDetail;