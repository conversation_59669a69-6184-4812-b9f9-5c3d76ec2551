import React, { useState, useEffect } from 'react';
import { Mail, Gift, Star, CheckCircle, AlertCircle, <PERSON>rkles, Clock, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

interface NewsletterSubscriptionProps {
  translations: {
    newsletter: {
      title: string;
      subtitle: string;
      emailPlaceholder: string;
      subscribe: string;
      subscribing: string;
      successTitle: string;
      successMessage: string;
      errorMessage: string;
      privacyNotice: string;
      exclusiveOffers: string;
      limitedTime: string;
      subscribersOnly: string;
      benefits: {
        earlyAccess: string;
        exclusiveDiscounts: string;
        expertTips: string;
        monthlyOffers: string;
        freeConsultation: string;
        priorityBooking: string;
      };
      incentives: {
        discount: string;
        freeConsult: string;
        earlyAccess: string;
        vipTreatment: string;
      };
      urgency: {
        limitedSpots: string;
        joinToday: string;
        spotsLeft: string;
        memberBenefit: string;
        timeLeft: string;
        hours: string;
        minutes: string;
      };
      socialProof: {
        membersJoined: string;
        thisWeek: string;
        satisfaction: string;
        rating: string;
      };
    };
  };
  variant?: 'default' | 'popup' | 'minimal' | 'urgent';
  showIncentives?: boolean;
  showSocialProof?: boolean;
  onClose?: () => void;
}

const NewsletterSubscription: React.FC<NewsletterSubscriptionProps> = ({
  translations,
  variant = 'default',
  showIncentives = true,
  showSocialProof = true,
  onClose
}) => {
  const [email, setEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [error, setError] = useState('');
  const [agreedToPrivacy, setAgreedToPrivacy] = useState(false);
  const [spotsLeft, setSpotsLeft] = useState(47);
  const [membersJoined, setMembersJoined] = useState(2847);
  const [timeLeft, setTimeLeft] = useState({ hours: 23, minutes: 45 });

  // Countdown timer for urgency
  useEffect(() => {
    if (variant === 'urgent') {
      const interval = setInterval(() => {
        setTimeLeft(prev => {
          let { hours, minutes } = prev;
          
          if (minutes > 0) {
            minutes--;
          } else if (hours > 0) {
            hours--;
            minutes = 59;
          }
          
          return { hours, minutes };
        });
      }, 60000); // Update every minute

      return () => clearInterval(interval);
    }
  }, [variant]);

  // Simulate real-time member count
  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.7) { // 30% chance
        setMembersJoined(prev => prev + 1);
        if (spotsLeft > 1) {
          setSpotsLeft(prev => prev - 1);
        }
      }
    }, 12000); // Every 12 seconds

    return () => clearInterval(interval);
  }, [spotsLeft]);

  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    if (!agreedToPrivacy) {
      setError('Please agree to our privacy policy');
      return;
    }

    setIsSubscribing(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsSubscribed(true);
      
      // Auto-close popup variant after success
      if (variant === 'popup' && onClose) {
        setTimeout(() => onClose(), 3000);
      }
    } catch (err) {
      setError(translations.newsletter.errorMessage);
    } finally {
      setIsSubscribing(false);
    }
  };

  const incentivesList = [
    {
      icon: <Gift className="h-5 w-5 text-primary-600" />,
      title: translations.newsletter.incentives.discount,
      description: "20% OFF first treatment"
    },
    {
      icon: <Users className="h-5 w-5 text-green-600" />,
      title: translations.newsletter.incentives.freeConsult,
      description: "Free 30-min consultation"
    },
    {
      icon: <Star className="h-5 w-5 text-yellow-600" />,
      title: translations.newsletter.incentives.earlyAccess,
      description: "Access to new treatments first"
    },
    {
      icon: <Sparkles className="h-5 w-5 text-purple-600" />,
      title: translations.newsletter.incentives.vipTreatment,
      description: "VIP member exclusive events"
    }
  ];

  const benefitsList = [
    { icon: <Star className="h-4 w-4" />, text: translations.newsletter.benefits.earlyAccess },
    { icon: <Gift className="h-4 w-4" />, text: translations.newsletter.benefits.exclusiveDiscounts },
    { icon: <Sparkles className="h-4 w-4" />, text: translations.newsletter.benefits.expertTips },
    { icon: <Mail className="h-4 w-4" />, text: translations.newsletter.benefits.monthlyOffers },
    { icon: <Users className="h-4 w-4" />, text: translations.newsletter.benefits.freeConsultation },
    { icon: <CheckCircle className="h-4 w-4" />, text: translations.newsletter.benefits.priorityBooking }
  ];

  if (isSubscribed) {
    return (
      <Card className={cn(
        "text-center",
        variant === 'popup' && "max-w-md mx-auto",
        variant === 'minimal' && "shadow-none border-none bg-transparent"
      )}>
        <CardContent className="p-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-2">
            {translations.newsletter.successTitle}
          </h3>
          <p className="text-muted-foreground mb-6">
            {translations.newsletter.successMessage}
          </p>
          
          {/* Success Incentives */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-primary-50 p-4 rounded-lg">
              <Gift className="h-6 w-6 text-primary-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-primary-700">20% OFF</div>
              <div className="text-xs text-primary-600">First Treatment</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <Users className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <div className="text-sm font-medium text-green-700">Free Consult</div>
              <div className="text-xs text-green-600">30 Minutes</div>
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            Check your email for exclusive offers!
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "relative overflow-hidden",
      variant === 'popup' && "max-w-2xl mx-auto shadow-2xl",
      variant === 'minimal' && "shadow-none border-none bg-transparent",
      variant === 'urgent' && "border-2 border-primary-200 shadow-xl"
    )}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-pink-50"></div>
      </div>

      {/* Close button for popup */}
      {variant === 'popup' && onClose && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="absolute top-4 right-4 z-10"
        >
          ×
        </Button>
      )}

      <CardHeader className={cn(
        "text-center",
        variant === 'urgent' && "bg-gradient-to-r from-primary-500 to-purple-600 text-white"
      )}>
        {/* Urgency Timer */}
        {variant === 'urgent' && (
          <div className="flex items-center justify-center gap-2 mb-4">
            <Clock className="h-4 w-4 text-yellow-300" />
            <span className="text-sm font-medium text-yellow-100">
              {translations.newsletter.urgency.timeLeft}: {timeLeft.hours}h {timeLeft.minutes}m
            </span>
          </div>
        )}

        <div className="flex items-center justify-center gap-3 mb-4">
          <div className={cn(
            "w-12 h-12 rounded-full flex items-center justify-center",
            variant === 'urgent' ? "bg-white/20" : "bg-primary-100"
          )}>
            <Mail className={cn(
              "h-6 w-6",
              variant === 'urgent' ? "text-white" : "text-primary-600"
            )} />
          </div>
          
          {showIncentives && (
            <>
              <Badge className={cn(
                "animate-pulse",
                variant === 'urgent' ? "bg-yellow-400 text-black" : "bg-primary-600 text-white"
              )}>
                <Gift className="h-3 w-3 mr-1" />
                {translations.newsletter.exclusiveOffers}
              </Badge>
              
              {variant === 'urgent' && (
                <Badge className="bg-red-500 text-white animate-bounce">
                  {translations.newsletter.limitedTime}
                </Badge>
              )}
            </>
          )}
        </div>

        <CardTitle className={cn(
          "text-2xl md:text-3xl font-bold mb-2",
          variant === 'urgent' ? "text-white" : "text-foreground"
        )}>
          {translations.newsletter.title}
        </CardTitle>
        
        <p className={cn(
          "text-lg",
          variant === 'urgent' ? "text-white/90" : "text-muted-foreground"
        )}>
          {translations.newsletter.subtitle}
        </p>

        {/* Urgency indicators */}
        {variant === 'urgent' && (
          <div className="flex items-center justify-center gap-6 mt-4 text-white/90">
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              <span className="text-sm">
                {translations.newsletter.urgency.spotsLeft}: 
                <span className="font-bold text-yellow-300 ml-1">{spotsLeft}</span>
              </span>
            </div>
            <div className="text-sm">
              {translations.newsletter.urgency.memberBenefit}
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Incentives Grid */}
        {showIncentives && variant !== 'minimal' && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {incentivesList.map((incentive, index) => (
              <div key={index} className="text-center p-4 bg-gradient-to-br from-primary-50 to-purple-50 rounded-lg">
                <div className="flex justify-center mb-2">
                  {incentive.icon}
                </div>
                <div className="text-sm font-medium text-foreground mb-1">
                  {incentive.title}
                </div>
                <div className="text-xs text-muted-foreground">
                  {incentive.description}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Benefits List */}
        {variant !== 'minimal' && (
          <div className="grid grid-cols-2 gap-3">
            {benefitsList.map((benefit, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <div className="text-primary-600">{benefit.icon}</div>
                <span className="text-muted-foreground">{benefit.text}</span>
              </div>
            ))}
          </div>
        )}

        {/* Social Proof */}
        {showSocialProof && variant !== 'minimal' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-green-700 font-medium">
                  {membersJoined} {translations.newsletter.socialProof.membersJoined}
                </span>
                <span className="text-green-600">
                  {translations.newsletter.socialProof.thisWeek}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                <span className="text-green-700 font-medium">4.9/5</span>
                <span className="text-green-600 text-xs">
                  {translations.newsletter.socialProof.satisfaction}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Subscription Form */}
        <form onSubmit={handleSubscribe} className="space-y-4">
          <div className="space-y-2">
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                type="email"
                placeholder={translations.newsletter.emailPlaceholder}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={cn(
                  "pl-10 h-12 text-lg",
                  error && "border-red-500"
                )}
                disabled={isSubscribing}
              />
            </div>
            
            {error && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="h-4 w-4" />
                {error}
              </div>
            )}
          </div>

          {/* Privacy Agreement */}
          <div className="flex items-start gap-2">
            <Checkbox
              id="privacy"
              checked={agreedToPrivacy}
              onCheckedChange={(checked) => setAgreedToPrivacy(checked === true)}
              className="mt-0.5"
            />
            <label htmlFor="privacy" className="text-sm text-muted-foreground leading-relaxed">
              {translations.newsletter.privacyNotice}
            </label>
          </div>

          <Button
            type="submit"
            className={cn(
              "w-full h-12 text-lg font-semibold",
              variant === 'urgent' && "bg-gradient-to-r from-primary-600 to-purple-600 hover:from-primary-700 hover:to-purple-700 animate-pulse"
            )}
            disabled={isSubscribing || !email || !agreedToPrivacy}
          >
            {isSubscribing ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                {translations.newsletter.subscribing}
              </>
            ) : (
              <>
                <Gift className="h-5 w-5 mr-2" />
                {translations.newsletter.subscribe}
              </>
            )}
          </Button>

          {variant === 'urgent' && (
            <div className="text-center">
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                {translations.newsletter.urgency.joinToday}
              </Badge>
            </div>
          )}
        </form>

        {/* Subscribers Only Badge */}
        {variant !== 'minimal' && (
          <div className="text-center">
            <Badge variant="outline" className="border-primary-200 text-primary-700">
              <Star className="h-3 w-3 mr-1" />
              {translations.newsletter.subscribersOnly}
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default NewsletterSubscription;