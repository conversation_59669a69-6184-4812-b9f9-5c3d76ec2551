import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LanguageSelector } from '@/components/LanguageSelector';

const FontDemo = () => {
  const { currentLanguage, t } = useLanguage();

  const sampleTexts = {
    th: {
      heading: 'ยินดีต้อนรับสู่คลินิกลูลลาบาย',
      subheading: 'คลินิกความงามชั้นนำที่ให้บริการด้วยเทคโนโลジีล่าสุด',
      body: 'เราเป็นคลินิกความงามที่มีประสบการณ์กว่า 15 ปี ด้วยทีมแพทย์ผู้เชี่ยวชาญและเทคโนโลยีล่าสุด เราพร้อมมอบการดูแลความงามที่ดีที่สุดให้กับลูกค้าทุกท่าน พร้อมด้วยบริการที่หลากหลายและมีคุณภาพสูง',
      small: 'บริการครบวงจร ด้วยใจที่ใส่ใจในทุกรายละเอียด'
    },
    en: {
      heading: 'Welcome to Lullaby Clinic',
      subheading: 'Premium Beauty Clinic with Cutting-Edge Technology',
      body: 'We are a beauty clinic with over 15 years of experience. With our team of expert doctors and the latest technology, we are ready to provide the best beauty care for all our customers. We offer diverse and high-quality services.',
      small: 'Comprehensive services with attention to every detail'
    },
    zh: {
      heading: '欢迎来到摇篮曲诊所',
      subheading: '采用尖端技术的高端美容诊所',
      body: '我们是一家拥有超过15年经验的美容诊所。凭借我们的专家医生团队和最新技术，我们准备为所有客户提供最好的美容护理。我们提供多样化和高质量的服务。',
      small: '全面的服务，关注每一个细节'
    }
  };

  const currentTexts = sampleTexts[currentLanguage];

  return (
    <div className="min-h-screen bg-gradient-soft p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">
            Font Demo - Multilingual Typography
          </h1>
          <p className="text-muted-foreground">
            Current Language: <span className="font-semibold">{currentLanguage.toUpperCase()}</span>
          </p>
          <LanguageSelector />
        </div>

        {/* Font Information Card */}
        <Card className="shadow-soft">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Font Information
              <span className="text-sm font-normal bg-primary-100 text-primary-700 px-2 py-1 rounded">
                {currentLanguage === 'th' ? 'Kanit' : 'Poppins'}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Thai Language</h4>
                <p className="text-sm text-muted-foreground">
                  Uses <strong>Kanit</strong> - A Thai font designed specifically for Thai script with excellent readability
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">English/Chinese</h4>
                <p className="text-sm text-muted-foreground">
                  Uses <strong>Poppins</strong> - A geometric sans-serif with excellent multilingual support
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Typography Samples */}
        <Card className="shadow-soft">
          <CardHeader>
            <CardTitle>Typography Samples</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Main Heading */}
            <div>
              <p className="text-xs text-muted-foreground uppercase tracking-wider mb-2">
                Main Heading (H1)
              </p>
              <h1 className="text-3xl md:text-4xl font-bold text-foreground leading-tight">
                {currentTexts.heading}
              </h1>
            </div>

            {/* Subheading */}
            <div>
              <p className="text-xs text-muted-foreground uppercase tracking-wider mb-2">
                Subheading (H2)
              </p>
              <h2 className="text-xl md:text-2xl font-semibold text-primary-600">
                {currentTexts.subheading}
              </h2>
            </div>

            {/* Body Text */}
            <div>
              <p className="text-xs text-muted-foreground uppercase tracking-wider mb-2">
                Body Text
              </p>
              <p className="text-base leading-relaxed text-foreground">
                {currentTexts.body}
              </p>
            </div>

            {/* Small Text */}
            <div>
              <p className="text-xs text-muted-foreground uppercase tracking-wider mb-2">
                Small Text
              </p>
              <p className="text-sm text-muted-foreground">
                {currentTexts.small}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Font Weight Samples */}
        <Card className="shadow-soft">
          <CardHeader>
            <CardTitle>Font Weight Variations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4">
              <div className="flex items-center gap-4">
                <span className="text-sm text-muted-foreground w-20">Light (300)</span>
                <span className="font-light text-lg">{t('nav.home')} - Light Weight</span>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-sm text-muted-foreground w-20">Regular (400)</span>
                <span className="font-normal text-lg">{t('nav.home')} - Regular Weight</span>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-sm text-muted-foreground w-20">Medium (500)</span>
                <span className="font-medium text-lg">{t('nav.home')} - Medium Weight</span>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-sm text-muted-foreground w-20">Semibold (600)</span>
                <span className="font-semibold text-lg">{t('nav.home')} - Semibold Weight</span>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-sm text-muted-foreground w-20">Bold (700)</span>
                <span className="font-bold text-lg">{t('nav.home')} - Bold Weight</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Usage Instructions */}
        <Card className="shadow-soft bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-800">How to Use</CardTitle>
          </CardHeader>
          <CardContent className="text-blue-700 space-y-2">
            <p>• Fonts are automatically applied based on the selected language</p>
            <p>• Thai content uses the Kanit font family</p>
            <p>• English and Chinese content use the Poppins font family</p>
            <p>• Use the language selector to switch between languages and see the font changes</p>
            <p>• Font classes (.font-kanit, .font-poppins) are available for manual application</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FontDemo;