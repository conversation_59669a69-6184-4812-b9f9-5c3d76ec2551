
import React, { useState } from 'react';
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface TestimonialsSectionProps {
  translations: any;
}

const TestimonialsSection = ({ translations }: TestimonialsSectionProps) => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      name: translations.testimonials.testimonial1.name,
      image: "/placeholder.svg",
      message: translations.testimonials.testimonial1.message,
      rating: 5,
      service: translations.testimonials.testimonial1.service
    },
    {
      name: translations.testimonials.testimonial2.name,
      image: "/placeholder.svg",
      message: translations.testimonials.testimonial2.message,
      rating: 5,
      service: translations.testimonials.testimonial2.service
    },
    {
      name: translations.testimonials.testimonial3.name,
      image: "/placeholder.svg",
      message: translations.testimonials.testimonial3.message,
      rating: 5,
      service: translations.testimonials.testimonial3.service
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section className="py-20 bg-medical-cream">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <Star className="h-4 w-4 mr-2" />
            {translations.testimonials.badge}
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {translations.testimonials.title}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {translations.testimonials.subtitle}
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Card className="bg-white shadow-soft">
            <CardContent className="p-8 md:p-12">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-center">
                {/* Client Photo */}
                <div className="text-center">
                  <div className="relative inline-block">
                    <img 
                      src={testimonials[currentTestimonial].image}
                      alt={testimonials[currentTestimonial].name}
                      className="w-32 h-32 rounded-full object-cover mx-auto border-4 border-primary-200"
                    />
                    <div className="absolute -top-2 -right-2 bg-primary-500 text-white p-2 rounded-full">
                      <Quote className="h-4 w-4" />
                    </div>
                  </div>
                  <h4 className="text-xl font-semibold mt-4 text-foreground">
                    {testimonials[currentTestimonial].name}
                  </h4>
                  <p className="text-sm text-primary-600 font-medium">
                    {testimonials[currentTestimonial].service}
                  </p>
                </div>

                {/* Testimonial Content */}
                <div className="md:col-span-2">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <blockquote className="text-lg text-muted-foreground leading-relaxed mb-6">
                    "{testimonials[currentTestimonial].message}"
                  </blockquote>

                  {/* Navigation */}
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      {testimonials.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentTestimonial(index)}
                          className={`w-3 h-3 rounded-full transition-colors ${
                            index === currentTestimonial 
                              ? 'bg-primary-500' 
                              : 'bg-primary-200 hover:bg-primary-300'
                          }`}
                        />
                      ))}
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={prevTestimonial}
                        className="border-primary-200 text-primary-600 hover:bg-primary-50"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={nextTestimonial}
                        className="border-primary-200 text-primary-600 hover:bg-primary-50"
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
