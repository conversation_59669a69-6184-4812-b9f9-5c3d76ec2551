import React from 'react';
import { useLanguage, type Language } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe } from 'lucide-react';

interface LanguageOption {
  code: Language;
  name: string;
  nativeName: string;
  flag: string;
}

const languageOptions: LanguageOption[] = [
  {
    code: 'th',
    name: 'Thai',
    nativeName: 'ไทย',
    flag: '🇹🇭',
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
  },
];

interface LanguageSelectorProps {
  variant?: 'default' | 'minimal';
  showText?: boolean;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'default',
  showText = true,
}) => {
  const { currentLanguage, changeLanguage } = useLanguage();

  const currentOption = languageOptions.find(
    (option) => option.code === currentLanguage
  );

  if (variant === 'minimal') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <span className="text-lg">{currentOption?.flag}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          {languageOptions.map((option) => (
            <DropdownMenuItem
              key={option.code}
              onClick={() => changeLanguage(option.code)}
              className={`flex items-center gap-2 cursor-pointer ${
                currentLanguage === option.code ? 'bg-accent' : ''
              }`}
            >
              <span className="text-base">{option.flag}</span>
              <span className="font-medium">{option.nativeName}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2 h-9 px-3"
        >
          <Globe className="h-4 w-4" />
          {showText && (
            <>
              <span className="text-sm">{currentOption?.flag}</span>
              <span className="hidden sm:inline text-sm font-medium">
                {currentOption?.nativeName}
              </span>
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {languageOptions.map((option) => (
          <DropdownMenuItem
            key={option.code}
            onClick={() => changeLanguage(option.code)}
            className={`flex items-center justify-between gap-3 cursor-pointer py-2 ${
              currentLanguage === option.code ? 'bg-accent' : ''
            }`}
          >
            <div className="flex items-center gap-2">
              <span className="text-base">{option.flag}</span>
              <span className="font-medium">{option.nativeName}</span>
            </div>
            <span className="text-xs text-muted-foreground">{option.name}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LanguageSelector;