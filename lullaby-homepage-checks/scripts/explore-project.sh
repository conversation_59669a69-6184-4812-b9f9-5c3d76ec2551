#!/bin/bash

# Lullaby Clinic - Quick Project Explorer
# Usage: ./scripts/explore-project.sh [option]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Header
echo -e "${PURPLE}🏥 Lullaby Clinic - Project Explorer${NC}"
echo -e "${CYAN}======================================${NC}"

# Function to show project structure
show_structure() {
    echo -e "\n${GREEN}📁 Project Structure:${NC}"
    tree -I 'node_modules|dist|.git' -L 3 2>/dev/null || {
        echo "Tree command not found, using find instead:"
        find . -path "./node_modules" -prune -o -path "./dist" -prune -o -path "./.git" -prune -o -type d -print | head -20 | sed 's|^\./|  |'
    }
}

# Function to show main components
show_components() {
    echo -e "\n${GREEN}⚛️  Main Components:${NC}"
    find ./src/components -name "*.tsx" -not -path "*/ui/*" | sort | while read file; do
        component_name=$(basename "$file" .tsx)
        echo -e "  ${BLUE}📦${NC} $component_name"
        # Extract first comment or interface for quick description
        description=$(head -10 "$file" | grep -E "//|interface.*Props" | head -1 | sed 's/^[[:space:]]*//' || echo "")
        if [ ! -z "$description" ]; then
            echo -e "     ${YELLOW}→${NC} $description"
        fi
    done
}

# Function to show UI components
show_ui_components() {
    echo -e "\n${GREEN}🎨 UI Components (shadcn/ui):${NC}"
    find ./src/components/ui -name "*.tsx" | sort | while read file; do
        component_name=$(basename "$file" .tsx)
        echo -e "  ${CYAN}🧩${NC} $component_name"
    done
}

# Function to show pages
show_pages() {
    echo -e "\n${GREEN}📄 Pages:${NC}"
    find ./src/pages -name "*.tsx" 2>/dev/null | sort | while read file; do
        page_name=$(basename "$file" .tsx)
        echo -e "  ${BLUE}📃${NC} $page_name"
    done
}

# Function to show utilities
show_utilities() {
    echo -e "\n${GREEN}🔧 Utilities & Contexts:${NC}"
    echo -e "  ${YELLOW}📂 Contexts:${NC}"
    find ./src/contexts -name "*.tsx" 2>/dev/null | while read file; do
        echo -e "    ${BLUE}🔗${NC} $(basename "$file" .tsx)"
    done
    
    echo -e "  ${YELLOW}📂 Hooks:${NC}"
    find ./src/hooks -name "*.ts*" 2>/dev/null | while read file; do
        echo -e "    ${BLUE}🎣${NC} $(basename "$file" | sed 's/\.(ts|tsx)$//')"
    done
    
    echo -e "  ${YELLOW}📂 Utils/Lib:${NC}"
    find ./src/lib ./src/utils -name "*.ts*" 2>/dev/null | while read file; do
        echo -e "    ${BLUE}⚙️${NC} $(basename "$file" | sed 's/\.(ts|tsx)$//')"
    done
}

# Function to show config files
show_config() {
    echo -e "\n${GREEN}⚙️  Configuration Files:${NC}"
    config_files=(
        "package.json:📦 Dependencies & Scripts"
        "tailwind.config.ts:🎨 Tailwind CSS Config"
        "tsconfig.json:📘 TypeScript Config"
        "vite.config.ts:⚡ Vite Build Config"
        ".cursorrules:🤖 AI Assistant Rules"
        "components.json:🧩 shadcn/ui Config"
    )
    
    for item in "${config_files[@]}"; do
        file="${item%%:*}"
        desc="${item##*:}"
        if [ -f "$file" ]; then
            echo -e "  ${BLUE}📄${NC} $file - $desc"
        fi
    done
}

# Function to show tech stack
show_tech_stack() {
    echo -e "\n${GREEN}🛠️  Tech Stack:${NC}"
    echo -e "  ${BLUE}⚛️${NC}  Framework: Next.js 15.3.3 + TypeScript"
    echo -e "  ${BLUE}🎨${NC}  Styling: TailwindCSS v3"
    echo -e "  ${BLUE}🧩${NC}  UI Components: shadcn/ui"
    echo -e "  ${BLUE}🗄️${NC}  Database: Supabase (Postgres + Storage)"
    echo -e "  ${BLUE}🌐${NC}  i18n: next-i18next (th, en, zh)"
    echo -e "  ${BLUE}🧪${NC}  Testing: Playwright (E2E)"
    echo -e "  ${BLUE}⚡${NC}  Build Tool: Vite"
}

# Function to show quick commands
show_commands() {
    echo -e "\n${GREEN}🚀 Quick Commands:${NC}"
    echo -e "  ${YELLOW}npm run dev${NC}      - Start development server"
    echo -e "  ${YELLOW}npm run build${NC}    - Build for production"
    echo -e "  ${YELLOW}npm run preview${NC}  - Preview production build"
    echo -e "  ${YELLOW}npm run lint${NC}     - Lint code"
    echo -e "  ${YELLOW}npm install${NC}      - Install dependencies"
}

# Function to find specific files
find_file() {
    local search_term=$1
    echo -e "\n${GREEN}🔍 Searching for files containing '$search_term':${NC}"
    find ./src -name "*.tsx" -o -name "*.ts" | xargs grep -l "$search_term" 2>/dev/null | head -10 | while read file; do
        echo -e "  ${BLUE}📁${NC} $file"
    done
}

# Main menu
case "${1:-all}" in
    "structure"|"s")
        show_structure
        ;;
    "components"|"c")
        show_components
        show_ui_components
        ;;
    "pages"|"p")
        show_pages
        ;;
    "utils"|"u")
        show_utilities
        ;;
    "config"|"cfg")
        show_config
        ;;
    "tech"|"t")
        show_tech_stack
        ;;
    "commands"|"cmd")
        show_commands
        ;;
    "find")
        if [ -z "$2" ]; then
            echo -e "${RED}Usage: $0 find <search_term>${NC}"
            exit 1
        fi
        find_file "$2"
        ;;
    "help"|"h")
        echo -e "\n${GREEN}Available options:${NC}"
        echo -e "  ${YELLOW}structure${NC} (s)   - Show project structure"
        echo -e "  ${YELLOW}components${NC} (c)  - Show all components"
        echo -e "  ${YELLOW}pages${NC} (p)       - Show pages"
        echo -e "  ${YELLOW}utils${NC} (u)       - Show utilities & contexts"
        echo -e "  ${YELLOW}config${NC} (cfg)    - Show configuration files"
        echo -e "  ${YELLOW}tech${NC} (t)        - Show tech stack"
        echo -e "  ${YELLOW}commands${NC} (cmd)  - Show quick commands"
        echo -e "  ${YELLOW}find${NC} <term>     - Find files containing term"
        echo -e "  ${YELLOW}all${NC} (default)  - Show everything"
        echo -e "  ${YELLOW}help${NC} (h)        - Show this help"
        ;;
    "all"|*)
        show_tech_stack
        show_structure
        show_components
        show_ui_components
        show_pages
        show_utilities
        show_config
        show_commands
        ;;
esac

echo -e "\n${CYAN}======================================${NC}"
echo -e "${PURPLE}💡 Tip: Use '$0 help' to see all options${NC}"